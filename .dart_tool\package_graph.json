{"roots": ["gps_attendance_app"], "packages": [{"name": "gps_attendance_app", "version": "1.0.0+1", "dependencies": ["camera", "cupertino_icons", "flutter", "geolocator", "http", "image", "shared_preferences"], "devDependencies": ["flutter_lints", "flutter_test"]}, {"name": "flutter_lints", "version": "6.0.0", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "image", "version": "4.5.4", "dependencies": ["archive", "meta", "xml"]}, {"name": "camera", "version": "0.10.6", "dependencies": ["camera_android", "camera_avfoundation", "camera_platform_interface", "camera_web", "flutter", "flutter_plugin_android_lifecycle"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "http", "version": "1.5.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "geolocator", "version": "14.0.2", "dependencies": ["flutter", "geolocator_android", "geolocator_apple", "geolocator_linux", "geolocator_platform_interface", "geolocator_web", "geolocator_windows"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "lints", "version": "6.0.0", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "xml", "version": "6.6.1", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "archive", "version": "4.0.7", "dependencies": ["crypto", "path", "posix"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.30", "dependencies": ["flutter"]}, {"name": "camera_web", "version": "0.3.5", "dependencies": ["camera_platform_interface", "flutter", "flutter_web_plugins", "stream_transform", "web"]}, {"name": "camera_platform_interface", "version": "2.10.0", "dependencies": ["cross_file", "flutter", "plugin_platform_interface", "stream_transform"]}, {"name": "camera_avfoundation", "version": "0.9.21+2", "dependencies": ["camera_platform_interface", "flutter", "stream_transform"]}, {"name": "camera_android", "version": "0.10.10+6", "dependencies": ["camera_platform_interface", "flutter", "flutter_plugin_android_lifecycle", "stream_transform"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.12", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "geolocator_linux", "version": "0.2.3", "dependencies": ["dbus", "flutter", "geoclue", "geolocator_platform_interface", "gsettings", "package_info_plus"]}, {"name": "geolocator_windows", "version": "0.2.5", "dependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_web", "version": "4.1.3", "dependencies": ["flutter", "flutter_web_plugins", "geolocator_platform_interface", "web"]}, {"name": "geolocator_apple", "version": "2.3.13", "dependencies": ["flutter", "geolocator_platform_interface"]}, {"name": "geolocator_android", "version": "5.0.2", "dependencies": ["flutter", "geolocator_platform_interface", "meta", "uuid"]}, {"name": "geolocator_platform_interface", "version": "4.2.6", "dependencies": ["flutter", "meta", "plugin_platform_interface", "vector_math"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "petitparser", "version": "7.0.1", "dependencies": ["collection", "meta"]}, {"name": "posix", "version": "6.0.3", "dependencies": ["ffi", "meta", "path"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "stream_transform", "version": "2.1.1", "dependencies": []}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "package_info_plus", "version": "8.3.1", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "gsettings", "version": "0.2.8", "dependencies": ["dbus", "xdg_directories"]}, {"name": "geoclue", "version": "0.1.1", "dependencies": ["dbus", "meta"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}, {"name": "package_info_plus_platform_interface", "version": "3.2.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}], "configVersion": 1}
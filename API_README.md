# GPS Attendance API Test Server

A Python Flask API server for testing the Flutter GPS attendance app.

## Features

- ✅ **Attendance Marking**: POST `/attendance` endpoint that matches Flutter app expectations
- 📍 **Location Validation**: Checks if user is within office radius
- 👥 **User Management**: Basic user CRUD operations
- 📊 **Attendance Records**: View all attendance records and user-specific records
- 🌐 **CORS Enabled**: Works with Flutter web apps
- 🧪 **Test Client**: Included test script to verify API functionality

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Start the API Server

```bash
python test_api.py
```

The server will start on `http://localhost:5000`

### 3. Test the API

```bash
python test_client.py
```

### 4. Update Flutter App

The Flutter app's API service has been updated to use `http://localhost:5000`. Just run your Flutter app:

```bash
flutter run -d chrome
```

## API Endpoints

### Core Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/` | API status and available endpoints |
| POST | `/attendance` | Mark attendance (matches Flutter app) |
| GET | `/attendance` | Get all attendance records |
| GET | `/attendance/<user_id>` | Get user-specific attendance |
| GET | `/users` | Get all users |
| POST | `/users` | Create new user |
| GET | `/config` | Get API configuration |

### Attendance Request Format

The Flutter app sends POST requests to `/attendance` with this format:

```json
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "latitude": 37.7749,
  "longitude": -122.4194,
  "accuracy": 10.0,
  "userId": "user123"
}
```

### Response Format

```json
{
  "success": true,
  "message": "Attendance marked successfully",
  "data": {
    "attendanceId": "uuid-here",
    "status": "present",
    "withinOfficeRadius": true,
    "distanceFromOffice": 25.5,
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

## Configuration

### Office Location

Edit the `OFFICE_LOCATION` in `test_api.py`:

```python
OFFICE_LOCATION = {
    "latitude": 37.7749,    # Your office latitude
    "longitude": -122.4194, # Your office longitude
    "radius": 100           # Allowed radius in meters
}
```

### Default Users

The API comes with two test users:
- `user123`: John Doe
- `user456`: Jane Smith

## Location Validation

The API calculates the distance between the user's location and the office location using the Haversine formula. If the user is within the configured radius, their status is marked as "present", otherwise "remote".

## Testing Different Scenarios

### Test Within Office Radius
```python
attendance_data = {
    "latitude": 37.7749,    # Same as office location
    "longitude": -122.4194,
    "userId": "user123"
}
```

### Test Outside Office Radius
```python
attendance_data = {
    "latitude": 40.7128,    # New York coordinates
    "longitude": -74.0060,
    "userId": "user123"
}
```

## Flutter App Integration

The Flutter app's `ApiService` has been updated to use the local test server:

```dart
class ApiService {
  static const String baseUrl = 'http://localhost:5000';
  // ... rest of the code
}
```

## Production Notes

This is a test server for development. For production:

1. Replace in-memory storage with a proper database
2. Add authentication and authorization
3. Add input validation and sanitization
4. Add logging and monitoring
5. Use environment variables for configuration
6. Add rate limiting and security headers

## Troubleshooting

### CORS Issues
The API includes CORS headers for web development. If you still have issues, check your browser's developer console.

### Connection Refused
Make sure the API server is running on port 5000 and your Flutter app is configured to use `http://localhost:5000`.

### Location Permission
For web testing, make sure you're using HTTPS or localhost, as browsers require secure contexts for location access.

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import '../services/location_service.dart';
import '../services/api_service.dart';
import '../services/user_service.dart';
import '../services/office_service.dart';
import '../services/camera_service.dart';
import '../theme/app_theme.dart';
import 'camera_screen.dart';

class AttendanceScreen extends StatefulWidget {
  const AttendanceScreen({super.key});

  @override
  State<AttendanceScreen> createState() => _AttendanceScreenState();
}

class _AttendanceScreenState extends State<AttendanceScreen> {
  final LocationService _locationService = LocationService();
  final ApiService _apiService = ApiService();
  final UserService _userService = UserService();
  final OfficeService _officeService = OfficeService();

  bool _isLoading = false;
  String? _statusMessage;
  Position? _currentLocation;
  String? _userName;
  String? _capturedPhoto;
  Map<String, dynamic>? _officeStatus;
  Map<String, dynamic>? _apiConfig;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    final userName = await _userService.getUserName();
    setState(() {
      _userName = userName;
    });
    await _loadApiConfig();
  }

  Future<void> _loadApiConfig() async {
    try {
      final apiEndpoint = await _userService.getApiEndpoint();
      final url = Uri.parse('$apiEndpoint/config');

      final response = await http.get(url);
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          _apiConfig = data['config'];
        });
      }
    } catch (e) {
      print('Error loading API config: $e');
    }
  }

  Future<void> _capturePhoto() async {
    try {
      // Initialize camera service
      await CameraService.initializeCameras();

      if (!CameraService.hasCameras) {
        setState(() {
          _statusMessage = 'No camera available on this device';
        });
        return;
      }

      // Navigate to camera screen
      if (!mounted) return;
      final photoBase64 = await Navigator.of(context).push<String>(
        MaterialPageRoute(
          builder: (context) => const CameraScreen(),
        ),
      );

      if (photoBase64 != null) {
        setState(() {
          _capturedPhoto = photoBase64;
          _statusMessage = 'Photo captured successfully!';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Error capturing photo: ${e.toString()}';
      });
    }
  }

  Future<void> _checkOfficeStatus() async {
    if (_currentLocation == null) return;

    try {
      final status = await _officeService.checkLocationStatus(
        _currentLocation!.latitude,
        _currentLocation!.longitude,
      );

      setState(() {
        _officeStatus = status;
      });
    } catch (e) {
      print('Error checking office status: $e');
    }
  }

  Future<void> _markAttendance() async {
    setState(() {
      _isLoading = true;
      _statusMessage = null;
    });

    try {
      // Check if photo is required from API config
      final requirePhoto = _apiConfig?['requirePhoto'] ?? false;
      final photoOptional = _apiConfig?['photoOptional'] ?? true;

      // If photo is required and not captured, capture it first
      if (requirePhoto && !photoOptional && _capturedPhoto == null) {
        setState(() {
          _statusMessage = 'Capturing photo...';
        });

        await _capturePhoto();

        // If still no photo after capture attempt, show error
        if (_capturedPhoto == null) {
          setState(() {
            _statusMessage =
                'Photo is required for attendance. Please try again.';
            _isLoading = false;
          });
          return;
        }
      }

      // If photo is required but optional, try to capture
      if (requirePhoto && photoOptional && _capturedPhoto == null) {
        setState(() {
          _statusMessage = 'Capturing photo (optional)...';
        });

        await _capturePhoto();
        // Continue even if photo capture fails
      }

      // Get current location
      final location = await _locationService.getCurrentLocation();
      setState(() {
        _currentLocation = location;
      });

      // Check office status
      await _checkOfficeStatus();

      // Mark attendance with photo if available
      final response = await _apiService.markAttendance(
        location,
        photoBase64: _capturedPhoto,
      );

      setState(() {
        final data = response['data'];
        final closestOffice = data['closestOffice'];
        final hasPhoto = data['hasPhoto'] ?? false;

        String message =
            response['message'] ?? 'Attendance marked successfully!';
        if (closestOffice != null) {
          message += '\n\nClosest office: ${closestOffice['name']}';
          message +=
              '\nDistance: ${closestOffice['distance'].toStringAsFixed(1)}m';
          message +=
              '\nStatus: ${closestOffice['within_radius'] ? 'Within office' : 'Remote'}';
        }
        if (hasPhoto) {
          message += '\n📸 Photo attached';
        }

        _statusMessage = message;

        // Clear captured photo after successful attendance
        _capturedPhoto = null;
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('GPS Attendance'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // User Welcome Card
            if (_userName != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      const Icon(Icons.person, size: 40, color: Colors.blue),
                      const SizedBox(width: 16),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('Welcome back,',
                              style: TextStyle(fontSize: 14)),
                          Text(
                            _userName!,
                            style: const TextStyle(
                                fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 20),

            // Location Information Card
            if (_currentLocation != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.location_on, color: Colors.green),
                          SizedBox(width: 8),
                          Text('Current Location',
                              style: TextStyle(
                                  fontSize: 16, fontWeight: FontWeight.bold)),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                          'Latitude: ${_currentLocation!.latitude.toStringAsFixed(6)}'),
                      Text(
                          'Longitude: ${_currentLocation!.longitude.toStringAsFixed(6)}'),
                      Text(
                          'Accuracy: ${_currentLocation!.accuracy.toStringAsFixed(1)}m'),
                      Text(
                          'Timestamp: ${DateTime.now().toString().split('.')[0]}'),
                    ],
                  ),
                ),
              ),

            const Spacer(),

            // API Configuration Info
            if (_apiConfig != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.settings, color: Colors.blue),
                          SizedBox(width: 8),
                          Text('Attendance Settings',
                              style: TextStyle(
                                  fontSize: 16, fontWeight: FontWeight.bold)),
                        ],
                      ),
                      const SizedBox(height: 12),
                      if (_apiConfig!['requirePhoto'] == true) ...[
                        Row(
                          children: [
                            Icon(
                              Icons.camera_alt,
                              color: _apiConfig!['photoOptional'] == true
                                  ? Colors.orange
                                  : Colors.red,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              _apiConfig!['photoOptional'] == true
                                  ? 'Photo capture: Optional'
                                  : 'Photo capture: Required',
                              style: TextStyle(
                                color: _apiConfig!['photoOptional'] == true
                                    ? Colors.orange
                                    : Colors.red,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ] else ...[
                        const Row(
                          children: [
                            Icon(Icons.no_photography,
                                color: Colors.grey, size: 16),
                            SizedBox(width: 8),
                            Text('Photo capture: Disabled',
                                style: TextStyle(color: Colors.grey)),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),
            ],

            // Main Attendance Button
            Center(
              child: Column(
                children: [
                  const Icon(
                    Icons.fingerprint,
                    size: 80,
                    color: Colors.blue,
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'Mark Your Attendance',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 40),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _markAttendance,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        textStyle: const TextStyle(fontSize: 18),
                      ),
                      child: _isLoading
                          ? const CircularProgressIndicator()
                          : const Text('Mark Attendance'),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Status Message
            if (_statusMessage != null)
              Card(
                color: _statusMessage!.contains('Error')
                    ? Colors.red.shade50
                    : Colors.green.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    _statusMessage!,
                    style: TextStyle(
                      color: _statusMessage!.contains('Error')
                          ? Colors.red
                          : Colors.green,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),

            const Spacer(),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import '../services/location_service.dart';
import '../services/api_service.dart';
import '../services/user_service.dart';

class AttendanceScreen extends StatefulWidget {
  const AttendanceScreen({super.key});

  @override
  State<AttendanceScreen> createState() => _AttendanceScreenState();
}

class _AttendanceScreenState extends State<AttendanceScreen> {
  final LocationService _locationService = LocationService();
  final ApiService _apiService = ApiService();
  final UserService _userService = UserService();

  bool _isLoading = false;
  String? _statusMessage;
  Position? _currentLocation;
  String? _userName;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    final userName = await _userService.getUserName();
    setState(() {
      _userName = userName;
    });
  }

  Future<void> _markAttendance() async {
    setState(() {
      _isLoading = true;
      _statusMessage = null;
    });

    try {
      final location = await _locationService.getCurrentLocation();
      setState(() {
        _currentLocation = location;
      });

      final response = await _apiService.markAttendance(location);

      setState(() {
        _statusMessage =
            response['message'] ?? 'Attendance marked successfully!';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('GPS Attendance'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // User Welcome Card
            if (_userName != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      const Icon(Icons.person, size: 40, color: Colors.blue),
                      const SizedBox(width: 16),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('Welcome back,',
                              style: TextStyle(fontSize: 14)),
                          Text(
                            _userName!,
                            style: const TextStyle(
                                fontSize: 18, fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 20),

            // Location Information Card
            if (_currentLocation != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.location_on, color: Colors.green),
                          SizedBox(width: 8),
                          Text('Current Location',
                              style: TextStyle(
                                  fontSize: 16, fontWeight: FontWeight.bold)),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                          'Latitude: ${_currentLocation!.latitude.toStringAsFixed(6)}'),
                      Text(
                          'Longitude: ${_currentLocation!.longitude.toStringAsFixed(6)}'),
                      Text(
                          'Accuracy: ${_currentLocation!.accuracy.toStringAsFixed(1)}m'),
                      Text(
                          'Timestamp: ${DateTime.now().toString().split('.')[0]}'),
                    ],
                  ),
                ),
              ),

            const Spacer(),

            // Main Attendance Button
            Center(
              child: Column(
                children: [
                  const Icon(
                    Icons.fingerprint,
                    size: 80,
                    color: Colors.blue,
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    'Mark Your Attendance',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 40),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _markAttendance,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        textStyle: const TextStyle(fontSize: 18),
                      ),
                      child: _isLoading
                          ? const CircularProgressIndicator()
                          : const Text('Mark Attendance'),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Status Message
            if (_statusMessage != null)
              Card(
                color: _statusMessage!.contains('Error')
                    ? Colors.red.shade50
                    : Colors.green.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    _statusMessage!,
                    style: TextStyle(
                      color: _statusMessage!.contains('Error')
                          ? Colors.red
                          : Colors.green,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),

            const Spacer(),
          ],
        ),
      ),
    );
  }
}

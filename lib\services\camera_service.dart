import 'dart:convert';
import 'dart:io';
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;

class CameraService {
  static List<CameraDescription>? _cameras;
  static CameraController? _controller;

  // Initialize cameras
  static Future<void> initializeCameras() async {
    try {
      _cameras = await availableCameras();
    } catch (e) {
      print('Error initializing cameras: $e');
      _cameras = [];
    }
  }

  // Get available cameras
  static List<CameraDescription> get cameras => _cameras ?? [];

  // Check if cameras are available
  static bool get hasCameras => _cameras != null && _cameras!.isNotEmpty;

  // Initialize camera controller
  static Future<CameraController?> initializeController({
    CameraLensDirection direction = CameraLensDirection.front,
  }) async {
    if (!hasCameras) {
      await initializeCameras();
      if (!hasCameras) return null;
    }

    try {
      // Find camera with specified direction
      final camera = _cameras!.firstWhere(
        (camera) => camera.lensDirection == direction,
        orElse: () => _cameras!.first,
      );

      _controller = CameraController(
        camera,
        ResolutionPreset.medium,
        enableAudio: false,
      );

      await _controller!.initialize();
      return _controller;
    } catch (e) {
      print('Error initializing camera controller: $e');
      return null;
    }
  }

  // Take picture and convert to base64
  static Future<String?> takePictureAsBase64() async {
    if (_controller == null || !_controller!.value.isInitialized) {
      throw Exception('Camera not initialized');
    }

    try {
      final XFile picture = await _controller!.takePicture();

      // Read image bytes
      Uint8List imageBytes;
      if (kIsWeb) {
        imageBytes = await picture.readAsBytes();
      } else {
        imageBytes = await File(picture.path).readAsBytes();
      }

      // Compress and resize image
      final compressedBytes = await _compressImage(imageBytes);

      // Convert to base64
      final base64String = base64Encode(compressedBytes);

      return base64String;
    } catch (e) {
      print('Error taking picture: $e');
      return null;
    }
  }

  // Compress image to reduce size
  static Future<Uint8List> _compressImage(Uint8List imageBytes) async {
    try {
      // Decode image
      img.Image? image = img.decodeImage(imageBytes);
      if (image == null) return imageBytes;

      // Resize image (max width/height: 800px)
      const maxSize = 800;
      if (image.width > maxSize || image.height > maxSize) {
        if (image.width > image.height) {
          image = img.copyResize(image, width: maxSize);
        } else {
          image = img.copyResize(image, height: maxSize);
        }
      }

      // Compress as JPEG with 85% quality
      final compressedBytes = img.encodeJpg(image, quality: 85);
      return Uint8List.fromList(compressedBytes);
    } catch (e) {
      print('Error compressing image: $e');
      return imageBytes;
    }
  }

  // Dispose camera controller
  static Future<void> dispose() async {
    if (_controller != null) {
      await _controller!.dispose();
      _controller = null;
    }
  }

  // Switch camera (front/back)
  static Future<CameraController?> switchCamera() async {
    if (!hasCameras || _cameras!.length < 2) return _controller;

    try {
      final currentDirection = _controller?.description.lensDirection;
      final newDirection = currentDirection == CameraLensDirection.front
          ? CameraLensDirection.back
          : CameraLensDirection.front;

      await dispose();
      return await initializeController(direction: newDirection);
    } catch (e) {
      print('Error switching camera: $e');
      return _controller;
    }
  }

  // Get camera preview widget
  static Widget? getCameraPreview() {
    if (_controller == null || !_controller!.value.isInitialized) {
      return null;
    }
    return CameraPreview(_controller!);
  }

  // Check if camera is initialized
  static bool get isInitialized =>
      _controller != null && _controller!.value.isInitialized;

  // Get current camera direction
  static CameraLensDirection? get currentDirection =>
      _controller?.description.lensDirection;
}

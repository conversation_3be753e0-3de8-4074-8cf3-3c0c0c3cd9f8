#!/usr/bin/env python3
"""
Test client for GPS Attendance API
Simulates Flutter app requests
"""

import requests
import json
from datetime import datetime

# API Configuration
API_BASE_URL = "http://localhost:5000"

def test_api():
    """Test the GPS Attendance API"""
    print("🧪 Testing GPS Attendance API...")
    
    # Test 1: Check API status
    print("\n1. Testing API status...")
    try:
        response = requests.get(f"{API_BASE_URL}/")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
        return
    
    # Test 2: Mark attendance (within office radius)
    print("\n2. Testing attendance marking (within office)...")
    attendance_data = {
        "timestamp": datetime.now().isoformat(),
        "latitude": 37.7749,  # San Francisco (same as office location)
        "longitude": -122.4194,
        "accuracy": 10.0,
        "userId": "user123"
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/attendance",
            json=attendance_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 3: Mark attendance (outside office radius)
    print("\n3. Testing attendance marking (outside office)...")
    attendance_data_remote = {
        "timestamp": datetime.now().isoformat(),
        "latitude": 40.7128,  # New York coordinates
        "longitude": -74.0060,
        "accuracy": 15.0,
        "userId": "user123"
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/attendance",
            json=attendance_data_remote,
            headers={"Content-Type": "application/json"}
        )
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 4: Get all attendance records
    print("\n4. Testing get all attendance...")
    try:
        response = requests.get(f"{API_BASE_URL}/attendance")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 5: Get user-specific attendance
    print("\n5. Testing get user attendance...")
    try:
        response = requests.get(f"{API_BASE_URL}/attendance/user123")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 6: Get users
    print("\n6. Testing get users...")
    try:
        response = requests.get(f"{API_BASE_URL}/users")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n✅ API testing completed!")

if __name__ == "__main__":
    test_api()

#!/usr/bin/env python3
"""
GPS Attendance API Test Server
A Python Flask API for testing the Flutter GPS attendance app
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
from datetime import datetime
import json
import os
import uuid

app = Flask(__name__)
CORS(app)  # Enable CORS for Flutter web app

# In-memory storage (use database in production)
attendance_records = []
users = {
    "user123": {"name": "<PERSON>", "email": "<EMAIL>"},
    "user456": {"name": "<PERSON>", "email": "<EMAIL>"}
}

# Configuration - Multiple Office Locations
OFFICE_LOCATIONS = {
    "main_office": {
        "name": "Main Office",
        "latitude": 37.7749,  # San Francisco coordinates
        "longitude": -122.4194,
        "radius": 100,  # meters
        "address": "123 Main St, San Francisco, CA"
    },
    "branch_office": {
        "name": "Branch Office",
        "latitude": 37.7849,  # Slightly different coordinates
        "longitude": -122.4094,
        "radius": 150,  # meters
        "address": "456 Branch Ave, San Francisco, CA"
    },
    "remote_office": {
        "name": "Remote Office",
        "latitude": 37.7649,
        "longitude": -122.4294,
        "radius": 200,  # meters
        "address": "789 Remote Rd, San Francisco, CA"
    }
}

# Default office for backward compatibility
OFFICE_LOCATION = OFFICE_LOCATIONS["main_office"]

def calculate_distance(lat1, lon1, lat2, lon2):
    """Calculate distance between two GPS coordinates in meters"""
    from math import radians, cos, sin, asin, sqrt
    
    # Convert decimal degrees to radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
    
    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))
    r = 6371000  # Radius of earth in meters
    return c * r

def is_within_office_radius(latitude, longitude):
    """Check if location is within any office radius"""
    closest_office = None
    min_distance = float('inf')
    within_radius = False

    for office_id, office in OFFICE_LOCATIONS.items():
        distance = calculate_distance(
            latitude, longitude,
            office["latitude"], office["longitude"]
        )

        if distance < min_distance:
            min_distance = distance
            closest_office = {
                "id": office_id,
                "name": office["name"],
                "address": office["address"],
                "distance": distance
            }

        if distance <= office["radius"]:
            within_radius = True
            closest_office["within_radius"] = True
        else:
            closest_office["within_radius"] = False

    return within_radius, min_distance, closest_office

@app.route('/', methods=['GET'])
def home():
    """API status endpoint"""
    return jsonify({
        "message": "GPS Attendance API is running",
        "version": "1.0.0",
        "endpoints": {
            "POST /attendance": "Mark attendance",
            "GET /attendance": "Get attendance records",
            "GET /attendance/<user_id>": "Get user attendance",
            "GET /users": "Get all users",
            "POST /users": "Create user"
        }
    })

@app.route('/attendance', methods=['POST'])
def mark_attendance():
    """Mark attendance endpoint - matches Flutter app expectation"""
    try:
        # Get JSON data from request
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        # Validate required fields
        required_fields = ['timestamp', 'latitude', 'longitude', 'userId']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400

        # Extract data
        user_id = data['userId']
        latitude = float(data['latitude'])
        longitude = float(data['longitude'])
        accuracy = data.get('accuracy', 0)
        timestamp = data['timestamp']
        photo_base64 = data.get('photo', None)  # Optional photo

        # Check if user exists
        if user_id not in users:
            return jsonify({"error": "User not found"}), 404

        # Check if location is within any office radius
        within_radius, distance, closest_office = is_within_office_radius(latitude, longitude)
        
        # Create attendance record
        attendance_record = {
            "id": str(uuid.uuid4()),
            "userId": user_id,
            "userName": users[user_id]["name"],
            "timestamp": timestamp,
            "location": {
                "latitude": latitude,
                "longitude": longitude,
                "accuracy": accuracy
            },
            "withinOfficeRadius": within_radius,
            "distanceFromOffice": round(distance, 2),
            "closestOffice": closest_office,
            "status": "present" if within_radius else "remote",
            "hasPhoto": photo_base64 is not None,
            "photoSize": len(photo_base64) if photo_base64 else 0,
            "createdAt": datetime.now().isoformat()
        }

        # Store photo separately if provided (in production, use proper file storage)
        if photo_base64:
            # In a real application, you'd save this to a file storage service
            # For demo purposes, we'll just store the size and first few characters
            attendance_record["photoPreview"] = photo_base64[:50] + "..." if len(photo_base64) > 50 else photo_base64
        
        # Store attendance record
        attendance_records.append(attendance_record)
        
        # Response
        response = {
            "success": True,
            "message": "Attendance marked successfully",
            "data": {
                "attendanceId": attendance_record["id"],
                "status": attendance_record["status"],
                "withinOfficeRadius": within_radius,
                "distanceFromOffice": round(distance, 2),
                "closestOffice": closest_office,
                "hasPhoto": photo_base64 is not None,
                "timestamp": timestamp
            }
        }
        
        return jsonify(response), 200
        
    except ValueError as e:
        return jsonify({"error": f"Invalid data format: {str(e)}"}), 400
    except Exception as e:
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@app.route('/attendance', methods=['GET'])
def get_all_attendance():
    """Get all attendance records"""
    return jsonify({
        "success": True,
        "data": attendance_records,
        "total": len(attendance_records)
    })

@app.route('/attendance/<user_id>', methods=['GET'])
def get_user_attendance(user_id):
    """Get attendance records for specific user with filtering and sorting"""
    try:
        # Check if user exists
        if user_id not in users:
            return jsonify({"error": "User not found"}), 404

        # Get query parameters for filtering
        limit = request.args.get('limit', type=int)
        offset = request.args.get('offset', default=0, type=int)
        date_from = request.args.get('from')  # Format: YYYY-MM-DD
        date_to = request.args.get('to')      # Format: YYYY-MM-DD
        status_filter = request.args.get('status')  # present, remote

        # Filter records for the user
        user_records = [record for record in attendance_records if record['userId'] == user_id]

        # Apply date filtering
        if date_from:
            try:
                from_date = datetime.fromisoformat(date_from + 'T00:00:00')
                user_records = [r for r in user_records
                              if datetime.fromisoformat(r['timestamp']) >= from_date]
            except ValueError:
                return jsonify({"error": "Invalid 'from' date format. Use YYYY-MM-DD"}), 400

        if date_to:
            try:
                to_date = datetime.fromisoformat(date_to + 'T23:59:59')
                user_records = [r for r in user_records
                              if datetime.fromisoformat(r['timestamp']) <= to_date]
            except ValueError:
                return jsonify({"error": "Invalid 'to' date format. Use YYYY-MM-DD"}), 400

        # Apply status filtering
        if status_filter:
            user_records = [r for r in user_records if r['status'].lower() == status_filter.lower()]

        # Sort by timestamp (newest first)
        user_records.sort(key=lambda x: x['timestamp'], reverse=True)

        # Apply pagination
        total_records = len(user_records)
        if limit:
            user_records = user_records[offset:offset + limit]
        elif offset > 0:
            user_records = user_records[offset:]

        # Calculate statistics
        stats = {
            "totalRecords": total_records,
            "presentCount": len([r for r in user_records if r['status'] == 'present']),
            "remoteCount": len([r for r in user_records if r['status'] == 'remote']),
            "averageDistance": 0
        }

        if user_records:
            distances = [r.get('distanceFromOffice', 0) for r in user_records]
            stats["averageDistance"] = round(sum(distances) / len(distances), 2)

        return jsonify({
            "success": True,
            "userId": user_id,
            "userName": users[user_id]["name"],
            "data": user_records,
            "pagination": {
                "total": total_records,
                "offset": offset,
                "limit": limit,
                "returned": len(user_records)
            },
            "statistics": stats
        })

    except Exception as e:
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@app.route('/users', methods=['GET'])
def get_users():
    """Get all users"""
    return jsonify({
        "success": True,
        "data": users
    })

@app.route('/users', methods=['POST'])
def create_user():
    """Create new user or login existing user"""
    try:
        data = request.get_json()

        if not data or 'userId' not in data or 'name' not in data:
            return jsonify({"error": "userId and name are required"}), 400

        user_id = data['userId']
        user_name = data['name']
        user_email = data.get('email', '')

        # Check if user already exists
        if user_id in users:
            # Update existing user information
            users[user_id].update({
                "name": user_name,
                "email": user_email,
                "lastLogin": datetime.now().isoformat()
            })

            return jsonify({
                "success": True,
                "message": "User logged in successfully",
                "data": {
                    "userId": user_id,
                    "name": users[user_id]["name"],
                    "email": users[user_id]["email"],
                    "isNewUser": False,
                    "lastLogin": users[user_id]["lastLogin"]
                }
            }), 200
        else:
            # Create new user
            users[user_id] = {
                "name": user_name,
                "email": user_email,
                "createdAt": datetime.now().isoformat(),
                "lastLogin": datetime.now().isoformat()
            }

            return jsonify({
                "success": True,
                "message": "User registered successfully",
                "data": {
                    "userId": user_id,
                    "name": users[user_id]["name"],
                    "email": users[user_id]["email"],
                    "isNewUser": True,
                    "createdAt": users[user_id]["createdAt"]
                }
            }), 201

    except Exception as e:
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@app.route('/users/<user_id>', methods=['GET'])
def get_user_details(user_id):
    """Get specific user details"""
    try:
        if user_id not in users:
            return jsonify({"error": "User not found"}), 404

        user_data = users[user_id].copy()

        # Add attendance statistics
        user_attendance = [r for r in attendance_records if r['userId'] == user_id]
        user_data['attendanceStats'] = {
            "totalAttendance": len(user_attendance),
            "presentCount": len([r for r in user_attendance if r['status'] == 'present']),
            "remoteCount": len([r for r in user_attendance if r['status'] == 'remote']),
            "lastAttendance": user_attendance[-1]['timestamp'] if user_attendance else None
        }

        return jsonify({
            "success": True,
            "data": user_data
        })

    except Exception as e:
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@app.route('/users/<user_id>', methods=['PUT'])
def update_user(user_id):
    """Update user information"""
    try:
        if user_id not in users:
            return jsonify({"error": "User not found"}), 404

        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Update allowed fields
        allowed_fields = ['name', 'email']
        for field in allowed_fields:
            if field in data:
                users[user_id][field] = data[field]

        users[user_id]['updatedAt'] = datetime.now().isoformat()

        return jsonify({
            "success": True,
            "message": "User updated successfully",
            "data": users[user_id]
        })

    except Exception as e:
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@app.route('/users/<user_id>', methods=['DELETE'])
def delete_user(user_id):
    """Delete user and their attendance records"""
    try:
        if user_id not in users:
            return jsonify({"error": "User not found"}), 404

        # Remove user
        del users[user_id]

        # Remove user's attendance records
        global attendance_records
        attendance_records = [r for r in attendance_records if r['userId'] != user_id]

        return jsonify({
            "success": True,
            "message": "User and attendance records deleted successfully"
        })

    except Exception as e:
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@app.route('/attendance/stats', methods=['GET'])
def get_attendance_stats():
    """Get overall attendance statistics"""
    try:
        total_records = len(attendance_records)
        present_count = len([r for r in attendance_records if r['status'] == 'present'])
        remote_count = len([r for r in attendance_records if r['status'] == 'remote'])

        # Get today's attendance
        today = datetime.now().date()
        today_records = [r for r in attendance_records
                        if datetime.fromisoformat(r['timestamp']).date() == today]

        # Get unique users who marked attendance today
        today_users = set(r['userId'] for r in today_records)

        return jsonify({
            "success": True,
            "data": {
                "totalRecords": total_records,
                "presentCount": present_count,
                "remoteCount": remote_count,
                "todayAttendance": len(today_records),
                "todayUniqueUsers": len(today_users),
                "totalUsers": len(users),
                "officeLocation": OFFICE_LOCATION
            }
        })

    except Exception as e:
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@app.route('/offices', methods=['GET'])
def get_offices():
    """Get all office locations"""
    return jsonify({
        "success": True,
        "data": OFFICE_LOCATIONS
    })

@app.route('/config', methods=['GET'])
def get_config():
    """Get API configuration"""
    return jsonify({
        "success": True,
        "config": {
            "officeLocations": OFFICE_LOCATIONS,
            "defaultOffice": OFFICE_LOCATION,
            "version": "1.1.0",
            "features": [
                "GPS Attendance Tracking",
                "User Registration",
                "Attendance History",
                "Location Validation",
                "Multiple Office Locations",
                "Photo Capture",
                "Statistics"
            ]
        }
    })

@app.route('/config', methods=['PUT'])
def update_config():
    """Update API configuration (office location and radius)"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        global OFFICE_LOCATION

        if 'latitude' in data:
            OFFICE_LOCATION['latitude'] = float(data['latitude'])
        if 'longitude' in data:
            OFFICE_LOCATION['longitude'] = float(data['longitude'])
        if 'radius' in data:
            OFFICE_LOCATION['radius'] = float(data['radius'])

        return jsonify({
            "success": True,
            "message": "Configuration updated successfully",
            "config": OFFICE_LOCATION
        })

    except ValueError as e:
        return jsonify({"error": f"Invalid numeric value: {str(e)}"}), 400
    except Exception as e:
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

if __name__ == '__main__':
    print("🚀 Starting GPS Attendance API Server...")
    print(f"📍 Office Location: {OFFICE_LOCATION['latitude']}, {OFFICE_LOCATION['longitude']}")
    print(f"📏 Allowed Radius: {OFFICE_LOCATION['radius']} meters")
    print("🌐 Server will run on http://localhost:5000")
    print("📱 Update your Flutter app to use: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)

#!/usr/bin/env python3
"""
GPS Attendance API Test Server
A Python Flask API for testing the Flutter GPS attendance app
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
from datetime import datetime
import json
import os
import uuid

app = Flask(__name__)
CORS(app)  # Enable CORS for Flutter web app

# In-memory storage (use database in production)
attendance_records = []
users = {
    "user123": {"name": "<PERSON>", "email": "<EMAIL>"},
    "user456": {"name": "<PERSON>", "email": "<EMAIL>"}
}

# Configuration
OFFICE_LOCATION = {
    "latitude": 37.7749,  # San Francisco coordinates (change to your office)
    "longitude": -122.4194,
    "radius": 100  # meters
}

def calculate_distance(lat1, lon1, lat2, lon2):
    """Calculate distance between two GPS coordinates in meters"""
    from math import radians, cos, sin, asin, sqrt
    
    # Convert decimal degrees to radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
    
    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))
    r = 6371000  # Radius of earth in meters
    return c * r

def is_within_office_radius(latitude, longitude):
    """Check if location is within office radius"""
    distance = calculate_distance(
        latitude, longitude,
        OFFICE_LOCATION["latitude"], OFFICE_LOCATION["longitude"]
    )
    return distance <= OFFICE_LOCATION["radius"], distance

@app.route('/', methods=['GET'])
def home():
    """API status endpoint"""
    return jsonify({
        "message": "GPS Attendance API is running",
        "version": "1.0.0",
        "endpoints": {
            "POST /attendance": "Mark attendance",
            "GET /attendance": "Get attendance records",
            "GET /attendance/<user_id>": "Get user attendance",
            "GET /users": "Get all users",
            "POST /users": "Create user"
        }
    })

@app.route('/attendance', methods=['POST'])
def mark_attendance():
    """Mark attendance endpoint - matches Flutter app expectation"""
    try:
        # Get JSON data from request
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        # Validate required fields
        required_fields = ['timestamp', 'latitude', 'longitude', 'userId']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"Missing required field: {field}"}), 400
        
        # Extract data
        user_id = data['userId']
        latitude = float(data['latitude'])
        longitude = float(data['longitude'])
        accuracy = data.get('accuracy', 0)
        timestamp = data['timestamp']
        
        # Check if user exists
        if user_id not in users:
            return jsonify({"error": "User not found"}), 404
        
        # Check if location is within office radius
        within_radius, distance = is_within_office_radius(latitude, longitude)
        
        # Create attendance record
        attendance_record = {
            "id": str(uuid.uuid4()),
            "userId": user_id,
            "userName": users[user_id]["name"],
            "timestamp": timestamp,
            "location": {
                "latitude": latitude,
                "longitude": longitude,
                "accuracy": accuracy
            },
            "withinOfficeRadius": within_radius,
            "distanceFromOffice": round(distance, 2),
            "status": "present" if within_radius else "remote",
            "createdAt": datetime.now().isoformat()
        }
        
        # Store attendance record
        attendance_records.append(attendance_record)
        
        # Response
        response = {
            "success": True,
            "message": "Attendance marked successfully",
            "data": {
                "attendanceId": attendance_record["id"],
                "status": attendance_record["status"],
                "withinOfficeRadius": within_radius,
                "distanceFromOffice": round(distance, 2),
                "timestamp": timestamp
            }
        }
        
        return jsonify(response), 200
        
    except ValueError as e:
        return jsonify({"error": f"Invalid data format: {str(e)}"}), 400
    except Exception as e:
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@app.route('/attendance', methods=['GET'])
def get_all_attendance():
    """Get all attendance records"""
    return jsonify({
        "success": True,
        "data": attendance_records,
        "total": len(attendance_records)
    })

@app.route('/attendance/<user_id>', methods=['GET'])
def get_user_attendance(user_id):
    """Get attendance records for specific user"""
    user_records = [record for record in attendance_records if record['userId'] == user_id]
    
    return jsonify({
        "success": True,
        "userId": user_id,
        "data": user_records,
        "total": len(user_records)
    })

@app.route('/users', methods=['GET'])
def get_users():
    """Get all users"""
    return jsonify({
        "success": True,
        "data": users
    })

@app.route('/users', methods=['POST'])
def create_user():
    """Create new user"""
    try:
        data = request.get_json()
        
        if not data or 'userId' not in data or 'name' not in data:
            return jsonify({"error": "userId and name are required"}), 400
        
        user_id = data['userId']
        if user_id in users:
            return jsonify({"error": "User already exists"}), 409
        
        users[user_id] = {
            "name": data['name'],
            "email": data.get('email', ''),
            "createdAt": datetime.now().isoformat()
        }
        
        return jsonify({
            "success": True,
            "message": "User created successfully",
            "data": users[user_id]
        }), 201
        
    except Exception as e:
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@app.route('/config', methods=['GET'])
def get_config():
    """Get API configuration"""
    return jsonify({
        "success": True,
        "config": {
            "officeLocation": OFFICE_LOCATION,
            "allowedRadius": OFFICE_LOCATION["radius"]
        }
    })

if __name__ == '__main__':
    print("🚀 Starting GPS Attendance API Server...")
    print(f"📍 Office Location: {OFFICE_LOCATION['latitude']}, {OFFICE_LOCATION['longitude']}")
    print(f"📏 Allowed Radius: {OFFICE_LOCATION['radius']} meters")
    print("🌐 Server will run on http://localhost:5000")
    print("📱 Update your Flutter app to use: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)

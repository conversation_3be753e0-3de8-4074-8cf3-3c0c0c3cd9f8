import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';

class ApiService {
  static const String baseUrl = 'http://localhost:5000';

  Future<void> markAttendance(Position location) async {
    final url = Uri.parse('$baseUrl/attendance');

    final body = {
      'timestamp': DateTime.now().toIso8601String(),
      'latitude': location.latitude,
      'longitude': location.longitude,
      'accuracy': location.accuracy,
      'userId': 'user123', // Replace with actual user ID
    };

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer your-token', // Add your auth token
      },
      body: json.encode(body),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to mark attendance: ${response.body}');
    }
  }
}

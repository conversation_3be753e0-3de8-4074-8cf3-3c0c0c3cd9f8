import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';
import 'user_service.dart';

class ApiService {
  final UserService _userService = UserService();

  Future<Map<String, dynamic>> markAttendance(Position location) async {
    final apiEndpoint = await _userService.getApiEndpoint();
    final userId = await _userService.getUserId();

    if (userId == null) {
      throw Exception('User not registered. Please register first.');
    }

    final url = Uri.parse('$apiEndpoint/attendance');

    final body = {
      'timestamp': DateTime.now().toIso8601String(),
      'latitude': location.latitude,
      'longitude': location.longitude,
      'accuracy': location.accuracy,
      'userId': userId,
    };

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
      },
      body: json.encode(body),
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to mark attendance: ${response.body}');
    }
  }

  Future<List<dynamic>> getAttendanceHistory() async {
    final apiEndpoint = await _userService.getApiEndpoint();
    final userId = await _userService.getUserId();

    if (userId == null) {
      throw Exception('User not registered. Please register first.');
    }

    final url = Uri.parse('$apiEndpoint/attendance/$userId');

    final response = await http.get(
      url,
      headers: {
        'Content-Type': 'application/json',
      },
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return data['data'] ?? [];
    } else {
      throw Exception('Failed to get attendance history: ${response.body}');
    }
  }
}

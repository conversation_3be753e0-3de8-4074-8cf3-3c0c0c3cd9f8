#!/usr/bin/env python3
"""
Test script to verify user registration endpoint
"""

import requests
import json

def test_registration():
    """Test the user registration endpoint"""
    api_url = "http://localhost:5000"
    
    print("🧪 Testing User Registration Endpoint...")
    print(f"API URL: {api_url}")
    
    # Test data (same format as Flutter app sends)
    test_user = {
        "userId": "test_user_123",
        "name": "Test User",
        "email": "<EMAIL>"
    }
    
    try:
        # Test 1: Check if API is running
        print("\n1. Testing API connectivity...")
        response = requests.get(f"{api_url}/")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ API is running")
        else:
            print("   ❌ API not responding correctly")
            return
        
        # Test 2: Check users endpoint (GET)
        print("\n2. Testing users endpoint (GET)...")
        response = requests.get(f"{api_url}/users")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Users endpoint accessible")
            data = response.json()
            print(f"   Current users: {len(data.get('data', {}))}")
        else:
            print("   ❌ Users endpoint not accessible")
            return
        
        # Test 3: Test user registration (POST)
        print("\n3. Testing user registration (POST)...")
        print(f"   Sending: {json.dumps(test_user, indent=2)}")
        
        response = requests.post(
            f"{api_url}/users",
            json=test_user,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code in [200, 201]:
            print("   ✅ User registration successful!")
            try:
                data = response.json()
                print(f"   Response data: {json.dumps(data, indent=2)}")
            except:
                print("   ⚠️  Response is not valid JSON")
        else:
            print("   ❌ User registration failed!")
            print(f"   Error: {response.text}")
        
        # Test 4: Test duplicate registration
        print("\n4. Testing duplicate user registration...")
        response = requests.post(
            f"{api_url}/users",
            json=test_user,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Duplicate user handled correctly (login)")
        elif response.status_code == 409:
            print("   ✅ Duplicate user detected correctly")
        else:
            print(f"   ⚠️  Unexpected status for duplicate: {response.status_code}")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API server!")
        print("   Make sure the Python API server is running:")
        print("   python test_api.py")
    except Exception as e:
        print(f"❌ Test failed with error: {e}")

if __name__ == "__main__":
    test_registration()

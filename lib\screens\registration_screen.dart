import 'package:flutter/material.dart';
import '../services/user_service.dart';
import 'main_screen.dart';

class RegistrationScreen extends StatefulWidget {
  const RegistrationScreen({super.key});

  @override
  State<RegistrationScreen> createState() => _RegistrationScreenState();
}

class _RegistrationScreenState extends State<RegistrationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _userIdController = TextEditingController();
  final _userNameController = TextEditingController();
  final _userEmailController = TextEditingController();
  final _apiEndpointController =
      TextEditingController(text: 'http://localhost:5000');

  final UserService _userService = UserService();
  bool _isLoading = false;
  bool _isTestingApi = false;
  String? _errorMessage;
  String? _apiTestMessage;
  List<String> _debugLogs = [];

  @override
  void dispose() {
    _userIdController.dispose();
    _userNameController.dispose();
    _userEmailController.dispose();
    _apiEndpointController.dispose();
    super.dispose();
  }

  Future<void> _testApiConnection() async {
    if (_apiEndpointController.text.trim().isEmpty) {
      setState(() {
        _apiTestMessage = 'Please enter an API endpoint first';
      });
      return;
    }

    setState(() {
      _isTestingApi = true;
      _apiTestMessage = null;
    });

    try {
      final result = await _userService
          .testApiConnection(_apiEndpointController.text.trim());
      setState(() {
        if (result['success']) {
          _apiTestMessage = '✅ ${result['message']}';
        } else {
          _apiTestMessage = '❌ ${result['message']}';
        }
      });
    } catch (e) {
      setState(() {
        _apiTestMessage = '❌ Connection test failed: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isTestingApi = false;
      });
    }
  }

  void _addDebugLog(String message) {
    setState(() {
      _debugLogs.add(
          '${DateTime.now().toString().split(' ')[1].split('.')[0]}: $message');
      if (_debugLogs.length > 10) {
        _debugLogs.removeAt(0);
      }
    });
    print('DEBUG: $message');
  }

  Future<void> _register() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _apiTestMessage = null;
      _debugLogs.clear();
    });

    _addDebugLog('Starting registration process...');
    _addDebugLog('User ID: ${_userIdController.text.trim()}');
    _addDebugLog('API Endpoint: ${_apiEndpointController.text.trim()}');

    try {
      _addDebugLog('Calling userService.registerUser...');

      final result = await _userService.registerUser(
        userId: _userIdController.text.trim(),
        userName: _userNameController.text.trim(),
        userEmail: _userEmailController.text.trim(),
        apiEndpoint: _apiEndpointController.text.trim(),
      );

      _addDebugLog('Registration successful!');
      _addDebugLog('Result: ${result.toString()}');

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const MainScreen()),
        );
      }
    } catch (e) {
      _addDebugLog('Registration failed: ${e.toString()}');
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('User Registration'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Icon(
                Icons.person_add,
                size: 80,
                color: Colors.blue,
              ),
              const SizedBox(height: 20),
              const Text(
                'Register for GPS Attendance',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),

              // User ID Field
              TextFormField(
                controller: _userIdController,
                decoration: const InputDecoration(
                  labelText: 'User ID',
                  hintText: 'Enter your unique user ID',
                  prefixIcon: Icon(Icons.badge),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a user ID';
                  }
                  if (value.trim().length < 3) {
                    return 'User ID must be at least 3 characters';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // User Name Field
              TextFormField(
                controller: _userNameController,
                decoration: const InputDecoration(
                  labelText: 'Full Name',
                  hintText: 'Enter your full name',
                  prefixIcon: Icon(Icons.person),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter your name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Email Field
              TextFormField(
                controller: _userEmailController,
                keyboardType: TextInputType.emailAddress,
                decoration: const InputDecoration(
                  labelText: 'Email',
                  hintText: 'Enter your email address',
                  prefixIcon: Icon(Icons.email),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter your email';
                  }
                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                      .hasMatch(value)) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // API Endpoint Field
              TextFormField(
                controller: _apiEndpointController,
                decoration: const InputDecoration(
                  labelText: 'API Endpoint',
                  hintText: 'Enter API server URL',
                  prefixIcon: Icon(Icons.link),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter API endpoint';
                  }
                  final uri = Uri.tryParse(value.trim());
                  if (uri == null || !uri.hasAbsolutePath) {
                    return 'Please enter a valid URL';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Test API Connection Button
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _isTestingApi ? null : _testApiConnection,
                  icon: _isTestingApi
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.wifi_find),
                  label: Text(
                      _isTestingApi ? 'Testing...' : 'Test API Connection'),
                ),
              ),

              // API Test Message
              if (_apiTestMessage != null) ...[
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _apiTestMessage!.startsWith('✅')
                        ? Colors.green.shade50
                        : Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _apiTestMessage!.startsWith('✅')
                          ? Colors.green
                          : Colors.red,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    _apiTestMessage!,
                    style: TextStyle(
                      color: _apiTestMessage!.startsWith('✅')
                          ? Colors.green.shade700
                          : Colors.red.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],

              const SizedBox(height: 30),

              // Register Button
              ElevatedButton(
                onPressed: _isLoading ? null : _register,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 15),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator()
                    : const Text('Register', style: TextStyle(fontSize: 16)),
              ),

              // Error Message
              if (_errorMessage != null) ...[
                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red, width: 1),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.error,
                              color: Colors.red.shade700, size: 20),
                          const SizedBox(width: 8),
                          const Text(
                            'Registration Error',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _errorMessage!,
                        style: TextStyle(color: Colors.red.shade700),
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        'Troubleshooting Tips:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '• Make sure the Python API server is running\n'
                        '• Check the API URL (should be http://localhost:5000)\n'
                        '• Try the "Test API Connection" button first\n'
                        '• Check your internet connection\n'
                        '• Ensure no firewall is blocking the connection',
                        style: TextStyle(
                          color: Colors.red.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Debug Logs Section (only show if there are logs)
              if (_debugLogs.isNotEmpty) ...[
                const SizedBox(height: 20),
                ExpansionTile(
                  title:
                      const Text('Debug Logs', style: TextStyle(fontSize: 14)),
                  children: [
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: _debugLogs
                            .map((log) => Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 2),
                                  child: Text(
                                    log,
                                    style: const TextStyle(
                                      fontSize: 12,
                                      fontFamily: 'monospace',
                                    ),
                                  ),
                                ))
                            .toList(),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class UserService {
  static const String _userIdKey = 'user_id';
  static const String _userNameKey = 'user_name';
  static const String _userEmailKey = 'user_email';
  static const String _apiEndpointKey = 'api_endpoint';
  static const String _isRegisteredKey = 'is_registered';

  // Get stored user ID
  Future<String?> getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userIdKey);
  }

  // Get stored user name
  Future<String?> getUserName() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userNameKey);
  }

  // Get stored user email
  Future<String?> getUserEmail() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userEmailKey);
  }

  // Get API endpoint
  Future<String> getApiEndpoint() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_apiEndpointKey) ?? 'http://localhost:5000';
  }

  // Check if user is registered
  Future<bool> isUserRegistered() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isRegisteredKey) ?? false;
  }

  // Save user data locally
  Future<void> saveUserData({
    required String userId,
    required String userName,
    required String userEmail,
    required String apiEndpoint,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userIdKey, userId);
    await prefs.setString(_userNameKey, userName);
    await prefs.setString(_userEmailKey, userEmail);
    await prefs.setString(_apiEndpointKey, apiEndpoint);
    await prefs.setBool(_isRegisteredKey, true);
  }

  // Update API endpoint
  Future<void> updateApiEndpoint(String apiEndpoint) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_apiEndpointKey, apiEndpoint);
  }

  // Register user with API
  Future<Map<String, dynamic>> registerUser({
    required String userId,
    required String userName,
    required String userEmail,
    required String apiEndpoint,
  }) async {
    final url = Uri.parse('$apiEndpoint/users');
    
    final body = {
      'userId': userId,
      'name': userName,
      'email': userEmail,
    };

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
      },
      body: json.encode(body),
    );

    if (response.statusCode == 201 || response.statusCode == 200) {
      // Save user data locally after successful registration
      await saveUserData(
        userId: userId,
        userName: userName,
        userEmail: userEmail,
        apiEndpoint: apiEndpoint,
      );
      
      return json.decode(response.body);
    } else if (response.statusCode == 409) {
      // User already exists, save locally anyway
      await saveUserData(
        userId: userId,
        userName: userName,
        userEmail: userEmail,
        apiEndpoint: apiEndpoint,
      );
      
      return {'success': true, 'message': 'User already exists, logged in successfully'};
    } else {
      throw Exception('Failed to register user: ${response.body}');
    }
  }

  // Get user data as a map
  Future<Map<String, String?>> getUserData() async {
    return {
      'userId': await getUserId(),
      'userName': await getUserName(),
      'userEmail': await getUserEmail(),
      'apiEndpoint': await getApiEndpoint(),
    };
  }

  // Clear user data (logout)
  Future<void> clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userIdKey);
    await prefs.remove(_userNameKey);
    await prefs.remove(_userEmailKey);
    await prefs.setBool(_isRegisteredKey, false);
  }
}

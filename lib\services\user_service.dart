import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class UserService {
  static const String _userIdKey = 'user_id';
  static const String _userNameKey = 'user_name';
  static const String _userEmailKey = 'user_email';
  static const String _userPhotoKey = 'user_photo';
  static const String _userDepartmentKey = 'user_department';
  static const String _userPositionKey = 'user_position';
  static const String _userLocationKey = 'user_location';
  static const String _apiEndpointKey = 'api_endpoint';
  static const String _isRegisteredKey = 'is_registered';

  // Get stored user ID
  Future<String?> getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userIdKey);
  }

  // Get stored user name
  Future<String?> getUserName() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userNameKey);
  }

  // Get stored user email
  Future<String?> getUserEmail() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userEmailKey);
  }

  // Get stored user photo path
  Future<String?> getUserPhoto() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userPhotoKey);
  }

  // Get stored user department
  Future<String?> getUserDepartment() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userDepartmentKey);
  }

  // Get stored user position
  Future<String?> getUserPosition() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userPositionKey);
  }

  // Get stored user location
  Future<String?> getUserLocation() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userLocationKey);
  }

  // Get API endpoint
  Future<String> getApiEndpoint() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_apiEndpointKey) ?? 'http://localhost:5000';
  }

  // Check if user is registered
  Future<bool> isUserRegistered() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isRegisteredKey) ?? false;
  }

  // Save user data locally
  Future<void> saveUserData({
    required String userId,
    required String userName,
    required String userEmail,
    required String apiEndpoint,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userIdKey, userId);
    await prefs.setString(_userNameKey, userName);
    await prefs.setString(_userEmailKey, userEmail);
    await prefs.setString(_apiEndpointKey, apiEndpoint);
    await prefs.setBool(_isRegisteredKey, true);
  }

  // Update API endpoint
  Future<void> updateApiEndpoint(String apiEndpoint) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_apiEndpointKey, apiEndpoint);
  }

  // Save user profile photo
  Future<void> saveUserPhoto(String photoPath) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userPhotoKey, photoPath);
  }

  // Update user profile data
  Future<void> updateUserProfile({
    String? department,
    String? position,
    String? location,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    if (department != null) {
      await prefs.setString(_userDepartmentKey, department);
    }
    if (position != null) {
      await prefs.setString(_userPositionKey, position);
    }
    if (location != null) {
      await prefs.setString(_userLocationKey, location);
    }
  }

  // Get complete user profile
  Future<Map<String, String?>> getUserProfile() async {
    return {
      'userId': await getUserId(),
      'name': await getUserName(),
      'email': await getUserEmail(),
      'photo': await getUserPhoto(),
      'department': await getUserDepartment(),
      'position': await getUserPosition(),
      'location': await getUserLocation(),
    };
  }

  // Register user with API
  Future<Map<String, dynamic>> registerUser({
    required String userId,
    required String userName,
    required String userEmail,
    required String apiEndpoint,
  }) async {
    try {
      // Clean up the API endpoint URL
      String cleanEndpoint = apiEndpoint.trim();
      if (cleanEndpoint.endsWith('/')) {
        cleanEndpoint = cleanEndpoint.substring(0, cleanEndpoint.length - 1);
      }

      final url = Uri.parse('$cleanEndpoint/users');
      // Debug logs
      print('=== USER REGISTRATION DEBUG ===');
      print('Clean endpoint: $cleanEndpoint');
      print('Full URL: $url');
      print('User ID: $userId');
      print('User Name: $userName');
      print('User Email: $userEmail');

      final body = {
        'userId': userId,
        'name': userName,
        'email': userEmail,
      };

      print('Request body: ${json.encode(body)}');
      print('Request headers: Content-Type: application/json');

      final response = await http
          .post(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode(body),
      )
          .timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception(
              'Connection timeout. Please check if the API server is running at $cleanEndpoint');
        },
      );

      print('Response status: ${response.statusCode}'); // Debug log
      print('Response body: ${response.body}'); // Debug log

      if (response.statusCode == 201 || response.statusCode == 200) {
        // Save user data locally after successful registration
        await saveUserData(
          userId: userId,
          userName: userName,
          userEmail: userEmail,
          apiEndpoint: cleanEndpoint,
        );

        final responseData = json.decode(response.body);
        return responseData;
      } else if (response.statusCode == 409) {
        // User already exists, save locally anyway
        await saveUserData(
          userId: userId,
          userName: userName,
          userEmail: userEmail,
          apiEndpoint: cleanEndpoint,
        );

        return {
          'success': true,
          'message': 'User already exists, logged in successfully'
        };
      } else {
        final errorBody =
            response.body.isNotEmpty ? response.body : 'Unknown error';
        throw Exception(
            'Registration failed (${response.statusCode}): $errorBody');
      }
    } catch (e) {
      print('=== REGISTRATION ERROR DEBUG ===');
      print('Error type: ${e.runtimeType}');
      print('Error message: ${e.toString()}');
      print('Stack trace available: ${e is Error}');

      if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection refused')) {
        throw Exception(
            'Cannot connect to API server at $apiEndpoint/users\n\nPlease check:\n• API server is running (python test_api.py)\n• URL is correct: $apiEndpoint\n• No firewall blocking the connection\n\nOriginal error: ${e.toString()}');
      } else if (e.toString().contains('FormatException')) {
        throw Exception(
            'Invalid API response format. The server may not be the expected GPS Attendance API.\n\nOriginal error: ${e.toString()}');
      } else if (e.toString().contains('timeout')) {
        throw Exception(
            'Connection timeout to $apiEndpoint/users\n\nThe API server may be slow or unreachable.\n\nOriginal error: ${e.toString()}');
      } else if (e.toString().contains('HandshakeException') ||
          e.toString().contains('TlsException')) {
        throw Exception(
            'SSL/TLS connection error. If using HTTP (not HTTPS), make sure the URL starts with http://\n\nOriginal error: ${e.toString()}');
      } else {
        throw Exception(
            'Registration failed with unexpected error:\n\n${e.toString()}\n\nPlease check the console for more details.');
      }
    }
  }

  // Test API connectivity and user endpoint
  Future<Map<String, dynamic>> testApiConnection(String apiEndpoint) async {
    try {
      String cleanEndpoint = apiEndpoint.trim();
      if (cleanEndpoint.endsWith('/')) {
        cleanEndpoint = cleanEndpoint.substring(0, cleanEndpoint.length - 1);
      }

      // Test 1: Basic API connectivity
      final rootUrl = Uri.parse('$cleanEndpoint/');
      final rootResponse = await http.get(rootUrl).timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          throw Exception('Connection timeout');
        },
      );

      if (rootResponse.statusCode != 200) {
        return {
          'success': false,
          'message': 'API root endpoint failed (${rootResponse.statusCode})',
          'data': null
        };
      }

      // Test 2: Test users endpoint with GET request
      final usersUrl = Uri.parse('$cleanEndpoint/users');
      final usersResponse = await http.get(usersUrl).timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          throw Exception('Users endpoint timeout');
        },
      );

      if (usersResponse.statusCode == 200) {
        return {
          'success': true,
          'message': 'API connection and users endpoint working',
          'data': {
            'rootStatus': rootResponse.statusCode,
            'usersStatus': usersResponse.statusCode,
            'endpoint': cleanEndpoint
          }
        };
      } else {
        return {
          'success': false,
          'message': 'Users endpoint failed (${usersResponse.statusCode})',
          'data': {
            'rootStatus': rootResponse.statusCode,
            'usersStatus': usersResponse.statusCode,
            'usersResponse': usersResponse.body
          }
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Connection test failed: ${e.toString()}',
        'data': null
      };
    }
  }

  // Get user data as a map
  Future<Map<String, String?>> getUserData() async {
    return {
      'userId': await getUserId(),
      'userName': await getUserName(),
      'userEmail': await getUserEmail(),
      'apiEndpoint': await getApiEndpoint(),
    };
  }

  // Clear user data (logout)
  Future<void> clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userIdKey);
    await prefs.remove(_userNameKey);
    await prefs.remove(_userEmailKey);
    await prefs.setBool(_isRegisteredKey, false);
  }
}

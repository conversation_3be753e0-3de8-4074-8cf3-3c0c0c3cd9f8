import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'user_service.dart';

class OfficeService {
  final UserService _userService = UserService();
  static const String _selectedOfficeKey = 'selected_office';

  // Get all office locations from API
  Future<Map<String, dynamic>> getOfficeLocations() async {
    try {
      final apiEndpoint = await _userService.getApiEndpoint();
      final url = Uri.parse('$apiEndpoint/offices');

      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
      ).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('Connection timeout while fetching office locations');
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data;
      } else {
        throw Exception('Failed to fetch office locations: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error fetching office locations: ${e.toString()}');
    }
  }

  // Get selected office from local storage
  Future<String?> getSelectedOffice() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_selectedOfficeKey);
  }

  // Set selected office in local storage
  Future<void> setSelectedOffice(String officeId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_selectedOfficeKey, officeId);
  }

  // Calculate distance between two points using Haversine formula
  double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371000; // Earth's radius in meters
    
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);
    
    final double a = 
        (sin(dLat / 2) * sin(dLat / 2)) +
        cos(_degreesToRadians(lat1)) * cos(_degreesToRadians(lat2)) *
        (sin(dLon / 2) * sin(dLon / 2));
    
    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    
    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  // Find closest office to current location
  Future<Map<String, dynamic>?> findClosestOffice(double latitude, double longitude) async {
    try {
      final officesData = await getOfficeLocations();
      final offices = officesData['data'] as Map<String, dynamic>;
      
      String? closestOfficeId;
      double minDistance = double.infinity;
      Map<String, dynamic>? closestOffice;
      
      for (final entry in offices.entries) {
        final officeId = entry.key;
        final office = entry.value as Map<String, dynamic>;
        
        final distance = calculateDistance(
          latitude,
          longitude,
          office['latitude'].toDouble(),
          office['longitude'].toDouble(),
        );
        
        if (distance < minDistance) {
          minDistance = distance;
          closestOfficeId = officeId;
          closestOffice = {
            'id': officeId,
            'name': office['name'],
            'address': office['address'],
            'latitude': office['latitude'],
            'longitude': office['longitude'],
            'radius': office['radius'],
            'distance': distance,
            'withinRadius': distance <= office['radius'].toDouble(),
          };
        }
      }
      
      return closestOffice;
    } catch (e) {
      print('Error finding closest office: $e');
      return null;
    }
  }

  // Check if location is within any office radius
  Future<Map<String, dynamic>> checkLocationStatus(double latitude, double longitude) async {
    try {
      final officesData = await getOfficeLocations();
      final offices = officesData['data'] as Map<String, dynamic>;
      
      List<Map<String, dynamic>> nearbyOffices = [];
      bool withinAnyOffice = false;
      
      for (final entry in offices.entries) {
        final officeId = entry.key;
        final office = entry.value as Map<String, dynamic>;
        
        final distance = calculateDistance(
          latitude,
          longitude,
          office['latitude'].toDouble(),
          office['longitude'].toDouble(),
        );
        
        final withinRadius = distance <= office['radius'].toDouble();
        if (withinRadius) withinAnyOffice = true;
        
        nearbyOffices.add({
          'id': officeId,
          'name': office['name'],
          'address': office['address'],
          'distance': distance,
          'withinRadius': withinRadius,
          'radius': office['radius'],
        });
      }
      
      // Sort by distance
      nearbyOffices.sort((a, b) => a['distance'].compareTo(b['distance']));
      
      return {
        'withinAnyOffice': withinAnyOffice,
        'nearbyOffices': nearbyOffices,
        'closestOffice': nearbyOffices.isNotEmpty ? nearbyOffices.first : null,
      };
    } catch (e) {
      throw Exception('Error checking location status: ${e.toString()}');
    }
  }

  // Get office details by ID
  Future<Map<String, dynamic>?> getOfficeById(String officeId) async {
    try {
      final officesData = await getOfficeLocations();
      final offices = officesData['data'] as Map<String, dynamic>;
      
      if (offices.containsKey(officeId)) {
        final office = offices[officeId] as Map<String, dynamic>;
        return {
          'id': officeId,
          ...office,
        };
      }
      
      return null;
    } catch (e) {
      print('Error getting office by ID: $e');
      return null;
    }
  }
}

// Import required math functions
import 'dart:math' show sin, cos, sqrt, atan2, pi;
